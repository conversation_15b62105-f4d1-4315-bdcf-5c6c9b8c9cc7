import { Home, Building2, MessageCircle, Heart, User } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";

const navItems = [
  { 
    icon: Home, 
    label: "Home", 
    path: "/" 
  },
  { 
    icon: Building2, 
    label: "Sell/Rent", 
    path: "/sell-rent" 
  },
  { 
    icon: MessageCircle, 
    label: "Chat", 
    path: "/chat" 
  },
  { 
    icon: Heart, 
    label: "Saved", 
    path: "/saved" 
  },
  { 
    icon: User, 
    label: "Profile", 
    path: "/profile" 
  },
];

const BottomNavigation = () => {
  const location = useLocation();

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border">
      <div className="mobile-container">
        <div className="flex items-center justify-around h-bottom-nav">
          {navItems.map(({ icon: Icon, label, path }) => {
            const isActive = location.pathname === path;
            
            return (
              <Link
                key={path}
                to={path}
                className={cn(
                  "flex flex-col items-center justify-center gap-1 py-2 px-3 rounded-lg transition-all duration-200 min-w-[60px]",
                  isActive 
                    ? "text-primary" 
                    : "text-muted-foreground hover:text-foreground"
                )}
              >
                <Icon 
                  size={20} 
                  className={cn(
                    "transition-transform duration-200",
                    isActive && "scale-110"
                  )}
                />
                <span className="text-xs font-medium">{label}</span>
                {isActive && (
                  <div className="absolute bottom-0 w-1 h-1 bg-primary rounded-full" />
                )}
              </Link>
            );
          })}
        </div>
      </div>
    </nav>
  );
};

export default BottomNavigation;