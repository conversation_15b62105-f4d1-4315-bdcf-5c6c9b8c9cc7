import { ReactNode } from "react";
import BottomNavigation from "./BottomNavigation";
import TopNavigation from "./TopNavigation";
import { cn } from "@/lib/utils";

interface MobileLayoutProps {
  children: ReactNode;
  showTopNav?: boolean;
  showBottomNav?: boolean;
  activeTopTab?: string;
  onTopTabChange?: (tab: string) => void;
  className?: string;
}

const MobileLayout = ({
  children,
  showTopNav = true,
  showBottomNav = true,
  activeTopTab,
  onTopTabChange,
  className,
}: MobileLayoutProps) => {
  return (
    <div className="mobile-container">
      {/* Top Navigation */}
      {showTopNav && (
        <TopNavigation 
          activeTab={activeTopTab}
          onTabChange={onTopTabChange}
        />
      )}

      {/* Main Content */}
      <main className={cn(
        "flex-1 min-h-screen",
        showBottomNav && "bottom-nav-spacing",
        className
      )}>
        {children}
      </main>

      {/* Bottom Navigation */}
      {showBottomNav && <BottomNavigation />}
    </div>
  );
};

export default MobileLayout;