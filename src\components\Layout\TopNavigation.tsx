import { useState } from "react";
import { cn } from "@/lib/utils";

const navTabs = [
  { id: "buy", label: "Buy" },
  { id: "rent", label: "Rent" },
  { id: "new-launch", label: "New Launch" },
  { id: "post-property", label: "Post Property" },
];

interface TopNavigationProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
}

const TopNavigation = ({ 
  activeTab = "buy", 
  onTabChange 
}: TopNavigationProps) => {
  const [currentTab, setCurrentTab] = useState(activeTab);

  const handleTabClick = (tabId: string) => {
    setCurrentTab(tabId);
    onTabChange?.(tabId);
  };

  return (
    <div className="bg-card border-b border-border sticky top-0 z-40">
      <div className="mobile-container">
        <div className="flex items-center gap-1 mobile-padding overflow-x-auto scrollbar-hide">
          {navTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              className={cn(
                "nav-tab flex-shrink-0 whitespace-nowrap",
                currentTab === tab.id ? "active" : ""
              )}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TopNavigation;