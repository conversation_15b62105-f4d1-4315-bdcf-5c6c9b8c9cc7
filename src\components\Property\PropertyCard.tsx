import { Heart, MapPin, Home as HomeIcon, Bath, Car } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface PropertyCardProps {
  id: string;
  title: string;
  price: string;
  location: string;
  propertyType: string;
  listingType: "rent" | "sale";
  bedrooms?: number;
  bathrooms?: number;
  parking?: boolean;
  area?: string;
  imageUrl?: string;
  isVerified?: boolean;
  isFavorited?: boolean;
  onFavoriteToggle?: (id: string) => void;
  onContactOwner?: (id: string) => void;
  className?: string;
}

const PropertyCard = ({
  id,
  title,
  price,
  location,
  propertyType,
  listingType,
  bedrooms,
  bathrooms,
  parking,
  area,
  imageUrl = "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?auto=format&fit=crop&w=400&q=80",
  isVerified = false,
  isFavorited = false,
  onFavoriteToggle,
  onContactOwner,
  className,
}: PropertyCardProps) => {
  return (
    <Card className={cn("property-card", className)}>
      {/* Property Image */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={imageUrl}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
        />
        
        {/* Listing Type Badge */}
        <Badge 
          className={cn(
            "absolute top-3 left-3 font-semibold",
            listingType === "rent" 
              ? "bg-rent text-white" 
              : "bg-sale text-white"
          )}
        >
          For {listingType === "rent" ? "Rent" : "Sale"}
        </Badge>

        {/* Verified Badge */}
        {isVerified && (
          <Badge className="absolute top-3 right-12 bg-success text-success-foreground">
            ✓ Verified
          </Badge>
        )}

        {/* Favorite Button */}
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-3 right-3 bg-background/80 hover:bg-background/90 text-foreground"
          onClick={() => onFavoriteToggle?.(id)}
        >
          <Heart 
            size={18} 
            className={cn(
              "transition-colors duration-200",
              isFavorited ? "fill-red-500 text-red-500" : "text-muted-foreground"
            )}
          />
        </Button>
      </div>

      {/* Property Details */}
      <div className="p-4 space-y-3">
        {/* Price */}
        <div className="flex items-center justify-between">
          <span className="price-display">₹{price}</span>
          {listingType === "rent" && (
            <span className="text-sm text-muted-foreground">/month</span>
          )}
        </div>

        {/* Title */}
        <h3 className="font-semibold text-foreground line-clamp-1">{title}</h3>

        {/* Location */}
        <div className="flex items-center gap-1 text-muted-foreground">
          <MapPin size={14} />
          <span className="text-sm line-clamp-1">{location}</span>
        </div>

        {/* Property Features */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          {bedrooms && (
            <div className="flex items-center gap-1">
              <HomeIcon size={14} />
              <span>{bedrooms} BHK</span>
            </div>
          )}
          {bathrooms && (
            <div className="flex items-center gap-1">
              <Bath size={14} />
              <span>{bathrooms}</span>
            </div>
          )}
          {parking && (
            <div className="flex items-center gap-1">
              <Car size={14} />
              <span>Parking</span>
            </div>
          )}
        </div>

        {/* Area & Property Type */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">{propertyType}</span>
          {area && (
            <span className="text-muted-foreground">{area}</span>
          )}
        </div>

        {/* Contact Button */}
        <Button 
          className="w-full mt-3 bg-accent hover:bg-accent/90 text-accent-foreground"
          onClick={() => onContactOwner?.(id)}
        >
          Contact Owner
        </Button>
      </div>
    </Card>
  );
};

export default PropertyCard;