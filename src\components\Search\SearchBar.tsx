import { Search, Filter, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";

interface SearchBarProps {
  onSearch?: (query: string) => void;
  onFilterToggle?: () => void;
  onMapToggle?: () => void;
  showMapToggle?: boolean;
  placeholder?: string;
}

const SearchBar = ({
  onSearch,
  onFilterToggle,
  onMapToggle,
  showMapToggle = true,
  placeholder = "Search for properties"
}: SearchBarProps) => {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  return (
    <div className="mobile-padding bg-card border-b border-border">
      <form onSubmit={handleSearch} className="flex items-center gap-2">
        {/* Search Input */}
        <div className="flex-1 relative">
          <Search 
            size={18} 
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" 
          />
          <Input
            type="text"
            placeholder={placeholder}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-11 bg-background border-border rounded-xl"
          />
        </div>

        {/* Filter Button */}
        <Button
          type="button"
          variant="outline"
          size="icon"
          className="h-11 w-11 rounded-xl"
          onClick={onFilterToggle}
        >
          <Filter size={18} />
        </Button>

        {/* Map Toggle Button */}
        {showMapToggle && (
          <Button
            type="button"
            variant="outline"
            size="icon"
            className="h-11 w-11 rounded-xl"
            onClick={onMapToggle}
          >
            <MapPin size={18} />
          </Button>
        )}
      </form>
    </div>
  );
};

export default SearchBar;