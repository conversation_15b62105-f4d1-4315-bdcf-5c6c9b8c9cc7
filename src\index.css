@tailwind base;
@tailwind components;
@tailwind utilities;

/* Propertywala Design System - Indian Property Marketplace
All colors MUST be HSL. Mobile-first, trust-building design.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 20% 10%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 20% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 20% 10%;

    /* Primary: Indian tech blue - trust and reliability */
    --primary: 214 90% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 214 90% 85%;
    --primary-dark: 214 90% 35%;

    /* Secondary: Clean neutral for cards and backgrounds */
    --secondary: 220 10% 96%;
    --secondary-foreground: 222.2 20% 15%;

    --muted: 220 10% 95%;
    --muted-foreground: 215 15% 50%;

    /* Accent: Trust-building orange for CTAs and highlights */
    --accent: 32 95% 60%;
    --accent-foreground: 222.2 20% 15%;
    --accent-light: 32 95% 85%;

    /* Success: Property verified/available */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 76% 85%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 214 90% 45%;

    --radius: 0.75rem;
    
    /* Property-specific colors */
    --price-color: 142 76% 36%;
    --rent-badge: 32 95% 60%;
    --sale-badge: 214 90% 45%;
    
    /* Gradients for mobile hero sections */
    --gradient-primary: linear-gradient(135deg, hsl(214 90% 45%), hsl(214 90% 35%));
    --gradient-accent: linear-gradient(135deg, hsl(32 95% 60%), hsl(32 95% 50%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 10% 98%));
    
    /* Shadows for mobile elevation */
    --shadow-card: 0 2px 8px hsl(222.2 20% 10% / 0.08);
    --shadow-floating: 0 8px 24px hsl(222.2 20% 10% / 0.12);
    --shadow-active: 0 4px 12px hsl(214 90% 45% / 0.2);
    
    /* Mobile-optimized spacing */
    --mobile-padding: 1rem;
    --mobile-gap: 0.75rem;
    
    /* Bottom navigation height */
    --bottom-nav-height: 4rem;
  }

  .dark {
    --background: 222.2 20% 8%;
    --foreground: 0 0% 98%;

    --card: 222.2 20% 10%;
    --card-foreground: 0 0% 98%;

    --popover: 222.2 20% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 214 90% 55%;
    --primary-foreground: 222.2 20% 8%;
    --primary-light: 214 90% 75%;
    --primary-dark: 214 90% 45%;

    --secondary: 222.2 15% 15%;
    --secondary-foreground: 0 0% 98%;

    --muted: 222.2 15% 12%;
    --muted-foreground: 215 15% 70%;

    --accent: 32 95% 65%;
    --accent-foreground: 222.2 20% 8%;
    --accent-light: 32 95% 80%;

    --success: 142 76% 46%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 76% 75%;

    --destructive: 0 84% 70%;
    --destructive-foreground: 222.2 20% 8%;

    --border: 222.2 15% 20%;
    --input: 222.2 15% 15%;
    --ring: 214 90% 55%;
    
    --gradient-primary: linear-gradient(135deg, hsl(214 90% 55%), hsl(214 90% 45%));
    --gradient-accent: linear-gradient(135deg, hsl(32 95% 65%), hsl(32 95% 55%));
    --gradient-card: linear-gradient(145deg, hsl(222.2 20% 10%), hsl(222.2 15% 12%));
    
    --shadow-card: 0 2px 8px hsl(0 0% 0% / 0.2);
    --shadow-floating: 0 8px 24px hsl(0 0% 0% / 0.3);
    --shadow-active: 0 4px 12px hsl(214 90% 55% / 0.3);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Mobile-first responsive design utilities */
  .mobile-container {
    @apply max-w-md mx-auto bg-background min-h-screen relative;
  }
  
  .mobile-padding {
    @apply px-4 py-3;
  }
  
  .bottom-nav-spacing {
    @apply pb-20;
  }
}

@layer components {
  /* Property card styles */
  .property-card {
    @apply bg-card rounded-xl border shadow-sm overflow-hidden transition-all duration-200;
  }
  
  .property-card:hover {
    @apply shadow-md transform -translate-y-1;
  }
  
  /* Price display */
  .price-display {
    @apply text-lg font-semibold;
    color: hsl(var(--price-color));
  }
  
  /* Navigation tab styles */
  .nav-tab {
    @apply px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200;
  }
  
  .nav-tab.active {
    @apply bg-primary text-primary-foreground shadow-sm;
  }
  
  .nav-tab:not(.active) {
    @apply text-muted-foreground hover:text-foreground hover:bg-secondary/50;
  }
}
