import MobileLayout from "@/components/Layout/MobileLayout";
import { MessageCircle, Phone, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

// Mock chat data
const mockChats = [
  {
    id: "1",
    propertyTitle: "3 BHK Apartment in Bandra West",
    ownerName: "<PERSON><PERSON> Sharma",
    lastMessage: "Property is available for immediate possession",
    timestamp: "2 min ago",
    isRead: false,
    propertyPrice: "₹85,000/month",
  },
  {
    id: "2",
    propertyTitle: "2 BHK Villa for Sale in Gurgaon",
    ownerName: "<PERSON><PERSON> Patel",
    lastMessage: "Sure, we can schedule a visit this weekend",
    timestamp: "1 hour ago",
    isRead: true,
    propertyPrice: "₹1.2 Cr",
  },
  {
    id: "3",
    propertyTitle: "1 BHK Studio in Koramangala",
    ownerName: "<PERSON><PERSON> <PERSON>",
    lastMessage: "Thanks for your interest. The rent is negotiable",
    timestamp: "3 hours ago",
    isRead: true,
    propertyPrice: "₹28,000/month",
  },
];

const Chat = () => {
  const handleWhatsAppOpen = (ownerName: string, propertyTitle: string) => {
    const message = `Hi ${ownerName}, I'm interested in your property: ${propertyTitle}`;
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
  };

  const handlePhoneCall = (ownerName: string) => {
    // In a real app, this would have actual phone numbers
    alert(`Calling ${ownerName}... (Phone integration not implemented in demo)`);
  };

  return (
    <MobileLayout showTopNav={false}>
      {/* Header */}
      <div className="bg-card border-b border-border sticky top-0 z-40">
        <div className="mobile-padding py-4">
          <h1 className="font-semibold text-xl">Chat</h1>
          <p className="text-sm text-muted-foreground">
            Connect with property owners
          </p>
        </div>
      </div>

      <div className="mobile-padding space-y-4">
        {/* WhatsApp Notice */}
        <Card className="p-4 bg-success-light/20 border-success-light">
          <div className="flex items-start gap-3">
            <MessageCircle size={20} className="text-success mt-0.5" />
            <div className="flex-1">
              <p className="text-sm font-medium text-success-foreground">
                WhatsApp Integration
              </p>
              <p className="text-xs text-success-foreground/80 mt-1">
                Chat directly with property owners via WhatsApp for faster communication
              </p>
            </div>
          </div>
        </Card>

        {/* Chat List */}
        {mockChats.map((chat) => (
          <Card key={chat.id} className="p-4 hover:shadow-md transition-shadow">
            <div className="space-y-3">
              {/* Chat Header */}
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                      {chat.ownerName.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-sm line-clamp-1">
                      {chat.propertyTitle}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {chat.ownerName}
                    </p>
                    <p className="text-xs font-medium text-price">
                      {chat.propertyPrice}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-muted-foreground">
                    {chat.timestamp}
                  </p>
                  {!chat.isRead && (
                    <div className="w-2 h-2 bg-accent rounded-full mt-1 ml-auto" />
                  )}
                </div>
              </div>

              {/* Last Message */}
              <div className="pl-13">
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {chat.lastMessage}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button
                  size="sm"
                  className="flex-1 bg-[#25D366] hover:bg-[#25D366]/90 text-white"
                  onClick={() => handleWhatsAppOpen(chat.ownerName, chat.propertyTitle)}
                >
                  <MessageCircle size={16} className="mr-2" />
                  WhatsApp
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handlePhoneCall(chat.ownerName)}
                >
                  <Phone size={16} />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                >
                  <ExternalLink size={16} />
                </Button>
              </div>
            </div>
          </Card>
        ))}

        {/* Empty State */}
        {mockChats.length === 0 && (
          <div className="text-center py-12">
            <MessageCircle size={48} className="mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="font-medium mb-2">No conversations yet</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Start browsing properties and connect with owners
            </p>
            <Button onClick={() => window.location.href = "/"}>
              Browse Properties
            </Button>
          </div>
        )}
      </div>
    </MobileLayout>
  );
};

export default Chat;