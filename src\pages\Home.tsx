import { useState } from "react";
import MobileLayout from "@/components/Layout/MobileLayout";
import SearchBar from "@/components/Search/SearchBar";
import PropertyCard from "@/components/Property/PropertyCard";
import { Button } from "@/components/ui/button";
import { MapPin, TrendingUp } from "lucide-react";

// Mock data for properties
const mockProperties = [
  {
    id: "1",
    title: "3 BHK Apartment in Bandra West",
    price: "85,000",
    location: "Bandra West, Mumbai",
    propertyType: "Apartment",
    listingType: "rent" as const,
    bedrooms: 3,
    bathrooms: 2,
    parking: true,
    area: "1250 sq ft",
    imageUrl: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?auto=format&fit=crop&w=400&q=80",
    isVerified: true,
    isFavorited: false,
  },
  {
    id: "2",
    title: "2 BHK Villa for Sale in Gurgaon",
    price: "1,20,00,000",
    location: "Sector 50, Gurgaon",
    propertyType: "Villa",
    listingType: "sale" as const,
    bedrooms: 2,
    bathrooms: 3,
    parking: true,
    area: "1800 sq ft",
    imageUrl: "https://images.unsplash.com/photo-1513584684374-8bab748fbf90?auto=format&fit=crop&w=400&q=80",
    isVerified: true,
    isFavorited: true,
  },
  {
    id: "3",
    title: "1 BHK Studio in Koramangala",
    price: "28,000",
    location: "Koramangala, Bangalore",
    propertyType: "Studio",
    listingType: "rent" as const,
    bedrooms: 1,
    bathrooms: 1,
    parking: false,
    area: "650 sq ft",
    imageUrl: "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?auto=format&fit=crop&w=400&q=80",
    isVerified: false,
    isFavorited: false,
  },
];

const Home = () => {
  const [activeTab, setActiveTab] = useState("buy");
  const [favorites, setFavorites] = useState<Set<string>>(new Set(["2"]));

  const handleFavoriteToggle = (propertyId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(propertyId)) {
        newFavorites.delete(propertyId);
      } else {
        newFavorites.add(propertyId);
      }
      return newFavorites;
    });
  };

  const handleContactOwner = (propertyId: string) => {
    // In a real app, this would open WhatsApp or a contact modal
    alert(`Opening WhatsApp to contact owner for property ${propertyId}`);
  };

  const filteredProperties = mockProperties.filter(property => {
    if (activeTab === "buy" || activeTab === "new-launch") {
      return property.listingType === "sale";
    }
    if (activeTab === "rent") {
      return property.listingType === "rent";
    }
    return true;
  });

  return (
    <MobileLayout 
      activeTopTab={activeTab}
      onTopTabChange={setActiveTab}
    >
      {/* Search Section */}
      <SearchBar 
        placeholder="Search properties in your area"
        onSearch={(query) => console.log("Searching:", query)}
        onFilterToggle={() => console.log("Open filters")}
        onMapToggle={() => console.log("Toggle map view")}
      />

      {/* Quick Location Access */}
      <div className="mobile-padding">
        <div className="flex items-center gap-2 mb-4">
          <MapPin size={16} className="text-primary" />
          <span className="text-sm font-medium">Popular Areas</span>
        </div>
        <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-2">
          {["Mumbai", "Delhi", "Bangalore", "Pune", "Chennai", "Hyderabad"].map((city) => (
            <Button
              key={city}
              variant="outline"
              size="sm"
              className="flex-shrink-0 rounded-full"
            >
              {city}
            </Button>
          ))}
        </div>
      </div>

      {/* Featured Properties Header */}
      <div className="mobile-padding pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp size={16} className="text-accent" />
            <h2 className="font-semibold">
              {activeTab === "rent" ? "Properties for Rent" : 
               activeTab === "new-launch" ? "New Launches" : "Properties for Sale"}
            </h2>
          </div>
          <Button variant="ghost" size="sm">
            View All
          </Button>
        </div>
      </div>

      {/* Properties Grid */}
      <div className="mobile-padding space-y-4">
        {filteredProperties.map((property) => (
          <PropertyCard
            key={property.id}
            {...property}
            isFavorited={favorites.has(property.id)}
            onFavoriteToggle={handleFavoriteToggle}
            onContactOwner={handleContactOwner}
          />
        ))}
      </div>

      {/* Empty State */}
      {filteredProperties.length === 0 && (
        <div className="mobile-padding text-center py-12">
          <div className="text-muted-foreground">
            <MapPin size={48} className="mx-auto mb-4 opacity-50" />
            <h3 className="font-medium mb-2">No properties found</h3>
            <p className="text-sm">
              Try changing your search criteria or explore different areas.
            </p>
          </div>
        </div>
      )}
    </MobileLayout>
  );
};

export default Home;