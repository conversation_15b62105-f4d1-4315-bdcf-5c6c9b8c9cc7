import MobileLayout from "@/components/Layout/MobileLayout";
import { User, Phone, Mail, MapPin, Building2, Heart, MessageCircle, Settings, LogOut, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

const Profile = () => {
  const userProfile = {
    name: "<PERSON><PERSON>",
    phone: "+91 98765 43210",
    email: "<EMAIL>",
    location: "Mumbai, Maharashtra",
    verified: true,
    memberSince: "March 2024",
    totalListings: 3,
    totalSaved: 8,
    totalChats: 12,
  };

  const menuItems = [
    {
      icon: Building2,
      label: "My Listings",
      description: "Manage your posted properties",
      count: userProfile.totalListings,
      href: "/my-listings",
    },
    {
      icon: Heart,
      label: "Saved Properties",
      description: "View your favorite properties",
      count: userProfile.totalSaved,
      href: "/saved",
    },
    {
      icon: MessageCircle,
      label: "Chat History",
      description: "Your conversations with owners",
      count: userProfile.totalChats,
      href: "/chat",
    },
    {
      icon: Settings,
      label: "Settings",
      description: "App preferences and notifications",
      href: "/settings",
    },
  ];

  return (
    <MobileLayout showTopNav={false}>
      {/* Header */}
      <div className="bg-gradient-primary text-primary-foreground">
        <div className="mobile-padding py-6">
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16 border-2 border-primary-foreground/20">
              <AvatarFallback className="bg-primary-foreground text-primary text-lg font-semibold">
                {userProfile.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h1 className="font-semibold text-lg">{userProfile.name}</h1>
                {userProfile.verified && (
                  <Badge className="bg-success text-success-foreground text-xs">
                    <Shield size={12} className="mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <p className="text-primary-foreground/80 text-sm">
                Member since {userProfile.memberSince}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mobile-padding space-y-6">
        {/* Contact Information */}
        <Card className="p-4 space-y-3">
          <h2 className="font-medium mb-3">Contact Information</h2>
          
          <div className="flex items-center gap-3">
            <Phone size={16} className="text-muted-foreground" />
            <span className="text-sm">{userProfile.phone}</span>
          </div>
          
          <div className="flex items-center gap-3">
            <Mail size={16} className="text-muted-foreground" />
            <span className="text-sm">{userProfile.email}</span>
          </div>
          
          <div className="flex items-center gap-3">
            <MapPin size={16} className="text-muted-foreground" />
            <span className="text-sm">{userProfile.location}</span>
          </div>
          
          <Button variant="outline" size="sm" className="w-full mt-3">
            Edit Profile
          </Button>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-3">
          <Card className="p-4 text-center">
            <div className="font-semibold text-lg text-primary">
              {userProfile.totalListings}
            </div>
            <div className="text-sm text-muted-foreground">Listings</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="font-semibold text-lg text-accent">
              {userProfile.totalSaved}
            </div>
            <div className="text-sm text-muted-foreground">Saved</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="font-semibold text-lg text-success">
              {userProfile.totalChats}
            </div>
            <div className="text-sm text-muted-foreground">Chats</div>
          </Card>
        </div>

        {/* Menu Items */}
        <div className="space-y-3">
          {menuItems.map((item) => (
            <Card key={item.label} className="p-4 hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-center gap-4">
                <div className="p-2 bg-secondary rounded-lg">
                  <item.icon size={20} className="text-secondary-foreground" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{item.label}</h3>
                    {item.count !== undefined && (
                      <Badge variant="secondary" className="text-xs">
                        {item.count}
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {item.description}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* App Information */}
        <Card className="p-4 space-y-3">
          <h3 className="font-medium">App Information</h3>
          <div className="space-y-2 text-sm text-muted-foreground">
            <div className="flex justify-between">
              <span>Version</span>
              <span>1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span>Last Updated</span>
              <span>Sept 18, 2024</span>
            </div>
          </div>
          
          <div className="pt-3 space-y-2">
            <Button variant="outline" size="sm" className="w-full justify-start">
              Help & Support
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start">
              Privacy Policy
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start">
              Terms of Service
            </Button>
          </div>
        </Card>

        {/* Logout */}
        <Button 
          variant="outline" 
          className="w-full border-destructive text-destructive hover:bg-destructive hover:text-destructive-foreground"
        >
          <LogOut size={16} className="mr-2" />
          Logout
        </Button>
      </div>
    </MobileLayout>
  );
};

export default Profile;