import MobileLayout from "@/components/Layout/MobileLayout";
import PropertyCard from "@/components/Property/PropertyCard";
import { Heart, Filter } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";

// Mock saved properties data
const mockSavedProperties = [
  {
    id: "2",
    title: "2 BHK Villa for Sale in Gurgaon",
    price: "1,20,00,000",
    location: "Sector 50, Gurgaon",
    propertyType: "Villa",
    listingType: "sale" as const,
    bedrooms: 2,
    bathrooms: 3,
    parking: true,
    area: "1800 sq ft",
    imageUrl: "https://images.unsplash.com/photo-1513584684374-8bab748fbf90?auto=format&fit=crop&w=400&q=80",
    isVerified: true,
    isFavorited: true,
    savedDate: "2 days ago",
  },
  {
    id: "4",
    title: "Luxury 4 BHK Apartment in South Delhi",
    price: "2,50,00,000",
    location: "Greater Kailash, New Delhi",
    propertyType: "Apartment",
    listingType: "sale" as const,
    bedrooms: 4,
    bathrooms: 3,
    parking: true,
    area: "2100 sq ft",
    imageUrl: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?auto=format&fit=crop&w=400&q=80",
    isVerified: true,
    isFavorited: true,
    savedDate: "1 week ago",
  },
];

const Saved = () => {
  const [favorites, setFavorites] = useState<Set<string>>(
    new Set(mockSavedProperties.map(p => p.id))
  );
  const [filter, setFilter] = useState<"all" | "rent" | "sale">("all");

  const handleFavoriteToggle = (propertyId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(propertyId)) {
        newFavorites.delete(propertyId);
      } else {
        newFavorites.add(propertyId);
      }
      return newFavorites;
    });
  };

  const handleContactOwner = (propertyId: string) => {
    // In a real app, this would open WhatsApp or a contact modal
    alert(`Opening WhatsApp to contact owner for property ${propertyId}`);
  };

  const filteredProperties = mockSavedProperties.filter(property => {
    if (filter === "all") return true;
    return property.listingType === filter;
  });

  const savedProperties = filteredProperties.filter(property => 
    favorites.has(property.id)
  );

  return (
    <MobileLayout showTopNav={false}>
      {/* Header */}
      <div className="bg-card border-b border-border sticky top-0 z-40">
        <div className="mobile-padding py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-semibold text-xl">Saved Properties</h1>
              <p className="text-sm text-muted-foreground">
                {savedProperties.length} properties saved
              </p>
            </div>
            <Button variant="outline" size="icon">
              <Filter size={18} />
            </Button>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="mobile-padding border-b border-border">
        <div className="flex gap-2 pb-3">
          {[
            { id: "all", label: "All" },
            { id: "rent", label: "For Rent" },
            { id: "sale", label: "For Sale" },
          ].map((tab) => (
            <Button
              key={tab.id}
              variant={filter === tab.id ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter(tab.id as any)}
              className="rounded-full"
            >
              {tab.label}
            </Button>
          ))}
        </div>
      </div>

      <div className="mobile-padding space-y-4">
        {/* Saved Properties */}
        {savedProperties.map((property) => (
          <div key={property.id} className="relative">
            <PropertyCard
              {...property}
              isFavorited={favorites.has(property.id)}
              onFavoriteToggle={handleFavoriteToggle}
              onContactOwner={handleContactOwner}
            />
            {/* Saved Date */}
            <div className="absolute top-2 left-2 bg-background/90 backdrop-blur-sm rounded-md px-2 py-1">
              <span className="text-xs text-muted-foreground">
                Saved {property.savedDate}
              </span>
            </div>
          </div>
        ))}

        {/* Empty State */}
        {savedProperties.length === 0 && (
          <div className="text-center py-12">
            <Heart size={48} className="mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="font-medium mb-2">No saved properties</h3>
            <p className="text-sm text-muted-foreground mb-4">
              {filter === "all" 
                ? "Start browsing and save properties you like"
                : `No ${filter === "rent" ? "rental" : "sale"} properties saved yet`
              }
            </p>
            <Button onClick={() => window.location.href = "/"}>
              Browse Properties
            </Button>
          </div>
        )}
      </div>
    </MobileLayout>
  );
};

export default Saved;