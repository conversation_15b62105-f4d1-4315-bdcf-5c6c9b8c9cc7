import { useState } from "react";
import { ArrowLeft, Camera, MapPin, Home, IndianRupee } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

const SellRent = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [listingType, setListingType] = useState<"sell" | "rent">("sell");
  const [formData, setFormData] = useState({
    title: "",
    location: "",
    propertyType: "",
    price: "",
    bedrooms: "",
    bathrooms: "",
    area: "",
    description: "",
    amenities: [] as string[],
  });

  const propertyTypes = [
    "Apartment",
    "Villa",
    "House",
    "Studio",
    "Office Space",
    "Shop",
    "Warehouse",
    "Plot"
  ];

  const amenitiesList = [
    "Parking", "Gym", "Swimming Pool", "Security", 
    "Power Backup", "Lift", "Garden", "Balcony"
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAmenityToggle = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.title || !formData.location || !formData.propertyType || !formData.price) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // In a real app, this would submit to backend
    toast({
      title: "Property Listed Successfully!",
      description: "Your property will be reviewed and published soon.",
    });
    
    // Reset form
    setFormData({
      title: "",
      location: "",
      propertyType: "",
      price: "",
      bedrooms: "",
      bathrooms: "",
      area: "",
      description: "",
      amenities: [],
    });
  };

  return (
    <div className="mobile-container min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card border-b border-border sticky top-0 z-40">
        <div className="flex items-center gap-3 mobile-padding">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate("/")}
            className="h-10 w-10"
          >
            <ArrowLeft size={20} />
          </Button>
          <h1 className="font-semibold text-lg">Add Property</h1>
        </div>
      </div>

      <div className="mobile-padding space-y-6 pb-8">
        {/* Listing Type Toggle */}
        <div className="space-y-3">
          <Label className="text-base font-medium">I want to</Label>
          <div className="flex gap-2">
            <Button
              type="button"
              variant={listingType === "sell" ? "default" : "outline"}
              onClick={() => setListingType("sell")}
              className="flex-1"
            >
              Sell
            </Button>
            <Button
              type="button"
              variant={listingType === "rent" ? "default" : "outline"}
              onClick={() => setListingType("rent")}
              className="flex-1"
            >
              Rent
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Property Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Property Title *</Label>
            <Input
              id="title"
              placeholder="e.g., 3 BHK Apartment in Bandra"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              className="h-11"
            />
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Location *</Label>
            <div className="relative">
              <MapPin size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                id="location"
                placeholder="Enter area, city"
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                className="pl-10 h-11"
              />
            </div>
          </div>

          {/* Property Type */}
          <div className="space-y-2">
            <Label>Property Type *</Label>
            <Select value={formData.propertyType} onValueChange={(value) => handleInputChange("propertyType", value)}>
              <SelectTrigger className="h-11">
                <SelectValue placeholder="Select property type" />
              </SelectTrigger>
              <SelectContent>
                {propertyTypes.map((type) => (
                  <SelectItem key={type} value={type.toLowerCase()}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Price */}
          <div className="space-y-2">
            <Label htmlFor="price">
              Price * {listingType === "rent" && "(per month)"}
            </Label>
            <div className="relative">
              <IndianRupee size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                id="price"
                type="number"
                placeholder={listingType === "rent" ? "25,000" : "50,00,000"}
                value={formData.price}
                onChange={(e) => handleInputChange("price", e.target.value)}
                className="pl-10 h-11"
              />
            </div>
          </div>

          {/* Property Details */}
          <div className="grid grid-cols-3 gap-3">
            <div className="space-y-2">
              <Label htmlFor="bedrooms">Bedrooms</Label>
              <Select value={formData.bedrooms} onValueChange={(value) => handleInputChange("bedrooms", value)}>
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="BHK" />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((num) => (
                    <SelectItem key={num} value={num.toString()}>
                      {num} BHK
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bathrooms">Bathrooms</Label>
              <Select value={formData.bathrooms} onValueChange={(value) => handleInputChange("bathrooms", value)}>
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Bath" />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((num) => (
                    <SelectItem key={num} value={num.toString()}>
                      {num}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="area">Area (sq ft)</Label>
              <Input
                id="area"
                type="number"
                placeholder="1200"
                value={formData.area}
                onChange={(e) => handleInputChange("area", e.target.value)}
                className="h-11"
              />
            </div>
          </div>

          {/* Photo Upload */}
          <div className="space-y-3">
            <Label>Upload Photos</Label>
            <Card className="border-2 border-dashed border-border hover:border-primary/50 transition-colors cursor-pointer">
              <div className="p-8 text-center">
                <Camera size={32} className="mx-auto mb-3 text-muted-foreground" />
                <p className="text-sm text-muted-foreground mb-1">
                  Tap to upload property photos
                </p>
                <p className="text-xs text-muted-foreground">
                  Add up to 10 photos (JPG, PNG)
                </p>
              </div>
            </Card>
          </div>

          {/* Amenities */}
          <div className="space-y-3">
            <Label>Amenities</Label>
            <div className="flex flex-wrap gap-2">
              {amenitiesList.map((amenity) => (
                <Badge
                  key={amenity}
                  variant={formData.amenities.includes(amenity) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-primary/90"
                  onClick={() => handleAmenityToggle(amenity)}
                >
                  {amenity}
                </Badge>
              ))}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe your property, nearby facilities, etc."
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* Submit Button */}
          <Button type="submit" className="w-full h-12 bg-accent hover:bg-accent/90 text-accent-foreground font-medium">
            Submit Property
          </Button>
        </form>

        {/* Note */}
        <Card className="p-4 bg-muted/50">
          <p className="text-sm text-muted-foreground">
            <span className="font-medium">Note:</span> Your property will be reviewed before going live. 
            We'll notify you once it's published.
          </p>
        </Card>
      </div>
    </div>
  );
};

export default SellRent;